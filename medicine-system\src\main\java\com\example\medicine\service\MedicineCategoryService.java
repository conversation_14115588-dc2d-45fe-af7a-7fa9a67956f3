package com.example.medicine.service;

import com.example.medicine.entity.MedicineCategory;
import java.util.List;

public interface MedicineCategoryService {
    
    /**
     * 获取所有药品分类
     */
    List<MedicineCategory> findAll();
    
    /**
     * 根据ID获取药品分类
     */
    MedicineCategory findById(Long id);
    
    /**
     * 根据名称获取药品分类
     */
    MedicineCategory findByName(String name);
    
    /**
     * 保存药品分类
     */
    MedicineCategory save(MedicineCategory category);
    
    /**
     * 删除药品分类
     */
    void deleteById(Long id);
    
    /**
     * 初始化默认分类数据
     */
    void initDefaultCategories();
}
