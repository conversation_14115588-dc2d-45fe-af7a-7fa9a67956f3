package com.example.medicine.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonSetter;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "medicine")
public class Medicine {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String name;
    private String spec;
    private String batchNo;
    private Date expireDate;
    private Double price;
    private Integer stock;

    // 数据库中存储语义化字符串状态
    private String status;
    private Long categoryId;

    // 状态常量定义
    public static final String STATUS_ONSALE = "ONSALE";   // 正常/在售
    public static final String STATUS_OFFSALE = "OFFSALE"; // 停用/下架

    /**
     * 将数据库中的字符串状态转换为前端需要的数字状态
     * ONSALE -> 1 (正常)
     * OFFSALE -> 0 (停用)
     */
    @JsonGetter("status")
    public Integer getStatusAsNumber() {
        if (STATUS_ONSALE.equals(this.status)) {
            return 1;
        } else if (STATUS_OFFSALE.equals(this.status)) {
            return 0;
        }
        // 默认返回1（正常状态）
        return 1;
    }

    /**
     * 将前端传入的数字状态转换为数据库需要的字符串状态
     * 1 -> ONSALE (正常)
     * 0 -> OFFSALE (停用)
     */
    @JsonSetter("status")
    public void setStatusFromNumber(Integer statusNumber) {
        if (statusNumber != null && statusNumber == 0) {
            this.status = STATUS_OFFSALE;
        } else {
            // 默认设置为正常状态
            this.status = STATUS_ONSALE;
        }
    }

    /**
     * 获取原始的字符串状态（用于数据库操作）
     */
    public String getRawStatus() {
        return this.status;
    }

    /**
     * 设置原始的字符串状态（用于数据库操作）
     */
    public void setRawStatus(String status) {
        this.status = status;
    }
}