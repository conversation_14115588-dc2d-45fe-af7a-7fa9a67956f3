<template>
  <div class="dashboard-page">
    <div class="page-header">
      <h1>🏥 医药管理系统 - 仪表盘</h1>
      <p>欢迎回来！登录成功！</p>
    </div>

    <el-card>
      <h2>🎉 恭喜！</h2>
      <p>您已成功登录医药管理系统！</p>
      <p>系统功能正在开发中...</p>

      <div class="quick-actions">
        <h3>快捷操作</h3>
        <el-button type="primary">药品管理</el-button>
        <el-button type="success">库存管理</el-button>
        <el-button type="warning">采购管理</el-button>
        <el-button type="info">销售管理</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 简单的响应式数据
const message = ref('Dashboard 加载成功！')
</script>

<style scoped>
.dashboard-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
  text-align: center;
}

.page-header h1 {
  color: #409EFF;
  margin-bottom: 10px;
}

.quick-actions {
  margin-top: 20px;
  text-align: center;
}

.quick-actions .el-button {
  margin: 0 10px 10px 0;
}
</style>