import axios from 'axios';
import { ElMessage } from 'element-plus';

const service = axios.create({
  baseURL: '/api',
  timeout: 10000,
});

// 请求拦截器，可加token
service.interceptors.request.use(
  config => {
    // 可在此处添加token等
    return config;
  },
  error => Promise.reject(error)
);

// 响应拦截器，统一处理Result结构
service.interceptors.response.use(
  response => {
    const res = response.data;

    // 检查是否是标准的Result格式
    if (res && typeof res === 'object' && 'code' in res) {
      if (res.code !== 200) {
        ElMessage.error(res.msg || '请求失败');
        return Promise.reject(res);
      }
      return res.data;
    }

    // 如果不是Result格式，直接返回数据（兼容旧接口）
    return res;
  },
  error => {
    ElMessage.error(error.message || '网络错误');
    return Promise.reject(error);
  }
);

export default service; 