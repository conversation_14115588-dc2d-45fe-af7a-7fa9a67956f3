package com.example.medicine.service.impl;

import com.example.medicine.entity.MedicineCategory;
import com.example.medicine.repository.MedicineCategoryRepository;
import com.example.medicine.service.MedicineCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;

@Service
public class MedicineCategoryServiceImpl implements MedicineCategoryService {

    @Autowired
    private MedicineCategoryRepository categoryRepository;

    @Override
    public List<MedicineCategory> findAll() {
        return categoryRepository.findAllOrderByCreateTime();
    }

    @Override
    public MedicineCategory findById(Long id) {
        return categoryRepository.findById(id).orElse(null);
    }

    @Override
    public MedicineCategory findByName(String name) {
        return categoryRepository.findByName(name);
    }

    @Override
    public MedicineCategory save(MedicineCategory category) {
        return categoryRepository.save(category);
    }

    @Override
    public void deleteById(Long id) {
        categoryRepository.deleteById(id);
    }

    @Override
    @PostConstruct
    public void initDefaultCategories() {
        // 检查是否已有分类数据，如果没有则初始化默认分类
        if (categoryRepository.count() == 0) {
            String[] defaultCategories = {
                "处方药", "非处方药", "中成药", "西药", "抗生素", 
                "维生素", "心血管药", "消化系统药", "呼吸系统药", "神经系统药"
            };
            
            String[] descriptions = {
                "需要医生处方才能购买的药品",
                "可以直接购买的非处方药品", 
                "中医药制剂",
                "化学合成药物",
                "抗菌消炎药物",
                "维生素类营养补充剂",
                "治疗心血管疾病的药物",
                "治疗消化系统疾病的药物",
                "治疗呼吸系统疾病的药物",
                "治疗神经系统疾病的药物"
            };
            
            for (int i = 0; i < defaultCategories.length; i++) {
                MedicineCategory category = new MedicineCategory();
                category.setName(defaultCategories[i]);
                category.setDescription(descriptions[i]);
                categoryRepository.save(category);
            }
        }
    }
}
