package com.example.medicine.repository;

import com.example.medicine.entity.MedicineCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface MedicineCategoryRepository extends JpaRepository<MedicineCategory, Long> {
    
    // 根据名称查找分类
    MedicineCategory findByName(String name);
    
    // 查找所有分类，按创建时间排序
    @Query("SELECT mc FROM MedicineCategory mc ORDER BY mc.createTime ASC")
    List<MedicineCategory> findAllOrderByCreateTime();
}
